import { NotificationAPI } from './datasources/NotificationAPI'
import { PostAPI } from './datasources/PostAPI'
import { UserAPI } from './datasources/UserAPI'
import { ApolloServer, ContextFunction } from '@apollo/server'
import { ExpressContextFunctionArgument } from '@apollo/server/express4'
import { Environment } from './common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { parseAuthJwt, UserJwtRole } from './auth'
import { MessageThreadAPI } from './datasources/MessageThreadAPI'
import { SubscriptionAPI } from './datasources/SubscriptionAPI'
import { LibraryAPI } from './datasources/LibraryAPI'
import { GjirafaAPI } from './datasources/GjirafaAPI'
import { StatisticsAPI } from './datasources/StatisticsAPI'
import { SessionAPI } from './datasources/SessionAPI'
import { UserMediaStoreAPI } from './datasources/UserMediaStoreAPI'
import { CategoriesAPI } from './datasources/CategoriesAPI'
import { NotificationSettingsAPI } from './datasources/NotificationSettingsAPI'
import { MediaAPI } from './datasources/MediaAPI'
import { SubscribeRequestAPI } from './datasources/SubscribeRequestAPI'

export type DataSourceContext = {
    dataSources: {
        notificationAPI: NotificationAPI
        notificationSettingsAPI: NotificationSettingsAPI
        postAPI: PostAPI
        userAPI: UserAPI
        messageThreadAPI: MessageThreadAPI
        mediaAPI: MediaAPI
        subscriptionAPI: SubscriptionAPI
        subscribeRequestAPI: SubscribeRequestAPI
        libraryAPI: LibraryAPI
        gjirafaAPI: GjirafaAPI
        statisticsAPI: StatisticsAPI
        userMediaStoreAPI: UserMediaStoreAPI
        categoriesAPI: CategoriesAPI
        sessionAPI: SessionAPI
    }
    user?: UserData
    req?: unknown
}

type UserData = {
    id: string
    role: UserJwtRole
    cookieExpiration: Date
}

export function contextFunction({
    server,
    env,
    gjirafa,
    internalApiKey,
}: ContextFunctionConfig): ContextFunction<[ExpressContextFunctionArgument], DataSourceContext> {
    return async ({ req }) => {
        const config: DataSourceConfig = {
            ...(server && { cache: server.cache }),
        }
        const cookies = req.headers.cookie
        const jwt = parseAuthJwt(cookies)

        const user: UserData | undefined = jwt
            ? {
                  id: jwt.sub,
                  role: jwt.ro == 1 ? 'moderator' : 'user',
                  cookieExpiration: new Date(jwt.exp * 1000),
              }
            : undefined

        const userId = jwt?.sub
        return {
            dataSources: {
                notificationAPI: new NotificationAPI(env, cookies, config),
                notificationSettingsAPI: new NotificationSettingsAPI(env, cookies, config),
                postAPI: new PostAPI(env, cookies, config),
                userAPI: new UserAPI(internalApiKey, env, cookies, config),
                messageThreadAPI: new MessageThreadAPI(env, cookies, config),
                mediaAPI: new MediaAPI(env, cookies, config),
                subscriptionAPI: new SubscriptionAPI(env, cookies, config),
                subscribeRequestAPI: new SubscribeRequestAPI(env, cookies, config),
                libraryAPI: new LibraryAPI(env, cookies, config),
                gjirafaAPI: new GjirafaAPI(gjirafa.apiKey, gjirafa.projectId, config),
                statisticsAPI: new StatisticsAPI(env, cookies, config),
                userMediaStoreAPI: new UserMediaStoreAPI(env, cookies, config),
                categoriesAPI: new CategoriesAPI(env, cookies, config),
                sessionAPI: new SessionAPI(env, cookies, config),
            },
            userId,
            ...{ user },
            req: req,
        }
    }
}

export type ContextFunctionConfig = {
    env: Environment
    server?: ApolloServer<DataSourceContext>
    internalApiKey: string
    gjirafa: {
        apiKey: string
        projectId: string
    }
}
