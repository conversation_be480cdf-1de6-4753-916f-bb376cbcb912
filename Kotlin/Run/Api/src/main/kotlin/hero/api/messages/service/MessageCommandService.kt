package hero.api.messages.service

import hero.api.post.service.CreatePost
import hero.api.post.service.PostService
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.model.MessageThread
import hero.model.Post
import java.time.Clock
import java.time.Instant

class MessageCommandService(
    // todo replace with repository
    private val messageThreadsCollection: TypedCollectionReference<MessageThread>,
    private val postService: PostService,
    private val clock: Clock = Clock.systemUTC(),
) {
    fun execute(command: SendMessage): Post {
        val messageThread = messageThreadsCollection[command.messageThreadId].get()

        if (command.userId !in messageThread.userIds) {
            throw ForbiddenException(
                "User ${command.userId} is not part of the message thread ${command.messageThreadId}",
            )
        }

        val post = postService.execute(
            CreatePost(
                userId = command.userId,
                messageThreadId = command.messageThreadId,
                publishedAt = Instant.now(clock),
                text = command.text,
                textHtml = null,
                textDelta = null,
                assets = listOf(),
                isSponsored = false,
                isAgeRestricted = false,
            ),
        )

        return post
    }
}

data class SendMessage(
    val userId: String,
    val messageThreadId: String,
    val text: String,
)
