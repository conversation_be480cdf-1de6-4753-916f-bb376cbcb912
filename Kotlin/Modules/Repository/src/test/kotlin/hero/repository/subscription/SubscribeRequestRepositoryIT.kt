package hero.repository.subscription

import hero.baseutils.truncated
import hero.model.SubscribeRequest
import hero.repository.RepositoryTest
import hero.repository.user
import hero.sql.jooq.Tables.SUBSCRIBE_REQUEST
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class SubscribeRequestRepositoryIT : RepositoryTest() {
    @Nested
    inner class Save {
        @Test
        fun `should save subscribe request to the database`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestRepository(testContext, TestClock(now))
            val createdAt = Instant.ofEpochSecond(1747831125)
            val acceptedAt = Instant.ofEpochSecond(1747831126)
            val declinedAt = Instant.ofEpochSecond(1747831127)
            val deletedAt = Instant.ofEpochSecond(1747831128)
            val seenAt = Instant.ofEpochSecond(1747831129)

            createUser(user("cestmir"))
            createUser(user("pablo"))
            val createdSubscribeRequest = underTest.save(
                SubscribeRequest(
                    userId = "pablo",
                    creatorId = "cestmir",
                    createdAt = createdAt,
                    acceptedAt = acceptedAt,
                    declinedAt = declinedAt,
                    deletedAt = deletedAt,
                    seenAt = seenAt,
                ),
            )

            val fetchResult = testContext
                .selectFrom(SUBSCRIBE_REQUEST)
                .fetchSingle()

            assertThat(createdSubscribeRequest.id).isEqualTo(fetchResult.id)
            assertThat(fetchResult.userId).isEqualTo("pablo")
            assertThat(fetchResult.creatorId).isEqualTo("cestmir")
            assertThat(fetchResult.createdAt).isEqualTo(createdAt)
            assertThat(fetchResult.updatedAt).isEqualTo(now)
            assertThat(fetchResult.acceptedAt).isEqualTo(acceptedAt)
            assertThat(fetchResult.declinedAt).isEqualTo(declinedAt)
            assertThat(fetchResult.deletedAt).isEqualTo(deletedAt)
            assertThat(fetchResult.seenAt).isEqualTo(seenAt)
        }
    }

    @Nested
    inner class FindSingle {
        @Test
        fun `should save subscribe request to the database`() {
            val underTest = SubscribeRequestRepository(testContext)

            createUser(user("cestmir"))
            createUser(user("pablo"))
            val subscribeRequest = SubscribeRequest(
                id = 5,
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = Instant.now().truncated(),
                acceptedAt = null,
                declinedAt = null,
                deletedAt = null,
                seenAt = null,
            )
            underTest.save(subscribeRequest)

            val result = underTest.findSingle {
                this
                    .where(SUBSCRIBE_REQUEST.USER_ID.eq("pablo"))
                    .and(SUBSCRIBE_REQUEST.CREATOR_ID.eq("cestmir"))
            }

            assertThat(result).isEqualTo(subscribeRequest)
        }
    }
}
